[{"F:\\POLITICA\\VS CODE\\frontend\\src\\index.js": "1", "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js": "2", "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js": "3", "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js": "4", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js": "5", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js": "6", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js": "7", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js": "8", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js": "9", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js": "10", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js": "11", "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js": "12", "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js": "13"}, {"size": 535, "mtime": 1748846816117, "results": "14", "hashOfConfig": "15"}, {"size": 1257, "mtime": 1748846905292, "results": "16", "hashOfConfig": "15"}, {"size": 362, "mtime": 1748846816119, "results": "17", "hashOfConfig": "15"}, {"size": 6888, "mtime": 1748846939864, "results": "18", "hashOfConfig": "15"}, {"size": 4048, "mtime": 1748847150646, "results": "19", "hashOfConfig": "15"}, {"size": 6927, "mtime": 1748846994544, "results": "20", "hashOfConfig": "15"}, {"size": 7567, "mtime": 1748847703887, "results": "21", "hashOfConfig": "15"}, {"size": 8341, "mtime": 1748847125043, "results": "22", "hashOfConfig": "15"}, {"size": 10301, "mtime": 1748847718439, "results": "23", "hashOfConfig": "15"}, {"size": 9013, "mtime": 1748847275184, "results": "24", "hashOfConfig": "15"}, {"size": 8844, "mtime": 1748847231371, "results": "25", "hashOfConfig": "15"}, {"size": 6776, "mtime": 1748847184221, "results": "26", "hashOfConfig": "15"}, {"size": 505, "mtime": 1748846957692, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dvg5l4", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\POLITICA\\VS CODE\\frontend\\src\\index.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\App.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\reportWebVitals.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\components\\Navbar.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Discussions.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Home.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Login.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\KnowledgeBase.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Register.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\Profile.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\ResearchRepository.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\pages\\LiveDebates.js", [], [], "F:\\POLITICA\\VS CODE\\frontend\\src\\lib\\supabase.js", [], []]