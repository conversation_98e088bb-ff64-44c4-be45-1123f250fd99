// Discussion forum routes
const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../lib/database');
const { verifyToken, optionalAuth, requireVerified } = require('../middleware/auth');

const router = express.Router();

// Get all discussion threads with pagination
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const category = req.query.category;
    const search = req.query.search;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];
    let paramCount = 1;

    if (category) {
      whereClause += ` WHERE dt.category = $${paramCount}`;
      params.push(category);
      paramCount++;
    }

    if (search) {
      const searchClause = ` ${whereClause ? 'AND' : 'WHERE'} (dt.title ILIKE $${paramCount} OR dt.description ILIKE $${paramCount})`;
      whereClause += searchClause;
      params.push(`%${search}%`);
      paramCount++;
    }

    const threadsQuery = `
      SELECT 
        dt.*,
        u.username as creator_name,
        COUNT(c.id) as comment_count,
        MAX(c.created_at) as last_activity
      FROM discussion_threads dt
      LEFT JOIN users u ON dt.creator_id = u.id
      LEFT JOIN comments c ON dt.id = c.thread_id AND c.is_deleted = false
      ${whereClause}
      GROUP BY dt.id, u.username
      ORDER BY dt.is_pinned DESC, last_activity DESC NULLS LAST, dt.created_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `;

    params.push(limit, offset);

    const countQuery = `
      SELECT COUNT(DISTINCT dt.id) as total
      FROM discussion_threads dt
      ${whereClause}
    `;

    const [threadsResult, countResult] = await Promise.all([
      query(threadsQuery, params),
      query(countQuery, params.slice(0, -2)) // Remove limit and offset for count
    ]);

    const totalThreads = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalThreads / limit);

    res.json({
      data: threadsResult.rows,
      pagination: {
        page,
        limit,
        total: totalThreads,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching discussion threads:', error);
    res.status(500).json({ error: 'Failed to fetch discussion threads' });
  }
});

// Get single thread with comments
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const threadId = req.params.id;

    // Get thread details
    const threadQuery = `
      SELECT 
        dt.*,
        u.username as creator_name,
        u.role as creator_role
      FROM discussion_threads dt
      LEFT JOIN users u ON dt.creator_id = u.id
      WHERE dt.id = $1
    `;

    const threadResult = await query(threadQuery, [threadId]);

    if (threadResult.rows.length === 0) {
      return res.status(404).json({ error: 'Thread not found' });
    }

    const thread = threadResult.rows[0];

    // Get comments with nested structure
    const commentsQuery = `
      WITH RECURSIVE comment_tree AS (
        -- Base case: top-level comments
        SELECT 
          c.*,
          u.username,
          u.role,
          0 as depth,
          ARRAY[c.created_at] as path
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.thread_id = $1 AND c.parent_comment_id IS NULL AND c.is_deleted = false
        
        UNION ALL
        
        -- Recursive case: replies
        SELECT 
          c.*,
          u.username,
          u.role,
          ct.depth + 1,
          ct.path || c.created_at
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        JOIN comment_tree ct ON c.parent_comment_id = ct.id
        WHERE c.is_deleted = false AND ct.depth < 5
      )
      SELECT * FROM comment_tree
      ORDER BY path
    `;

    const commentsResult = await query(commentsQuery, [threadId]);

    // Update view count
    await query(
      'UPDATE discussion_threads SET view_count = view_count + 1 WHERE id = $1',
      [threadId]
    );

    res.json({
      thread: {
        ...thread,
        view_count: thread.view_count + 1
      },
      comments: commentsResult.rows
    });
  } catch (error) {
    console.error('Error fetching thread:', error);
    res.status(500).json({ error: 'Failed to fetch thread' });
  }
});

// Create new discussion thread
router.post('/',
  verifyToken,
  [
    body('title').trim().isLength({ min: 5, max: 255 }).escape(),
    body('description').optional().trim().isLength({ max: 2000 }).escape(),
    body('category').trim().isLength({ min: 1, max: 100 }).escape(),
    body('tags').optional().isArray({ max: 10 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { title, description, category, tags } = req.body;

      const insertQuery = `
        INSERT INTO discussion_threads (title, description, creator_id, category, tags)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `;

      const result = await query(insertQuery, [
        title,
        description || null,
        req.user.id,
        category,
        tags || []
      ]);

      const newThread = result.rows[0];

      res.status(201).json({
        message: 'Thread created successfully',
        thread: {
          ...newThread,
          creator_name: req.user.username,
          creator_role: req.user.role
        }
      });
    } catch (error) {
      console.error('Error creating thread:', error);
      res.status(500).json({ error: 'Failed to create thread' });
    }
  }
);

// Update thread (only creator or admin)
router.put('/:id',
  verifyToken,
  [
    body('title').optional().trim().isLength({ min: 5, max: 255 }).escape(),
    body('description').optional().trim().isLength({ max: 2000 }).escape(),
    body('category').optional().trim().isLength({ min: 1, max: 100 }).escape(),
    body('tags').optional().isArray({ max: 10 }),
    body('is_pinned').optional().isBoolean(),
    body('is_locked').optional().isBoolean()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const threadId = req.params.id;

      // Check if thread exists and user has permission
      const threadResult = await query(
        'SELECT * FROM discussion_threads WHERE id = $1',
        [threadId]
      );

      if (threadResult.rows.length === 0) {
        return res.status(404).json({ error: 'Thread not found' });
      }

      const thread = threadResult.rows[0];

      // Check permissions
      if (thread.creator_id !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ error: 'Permission denied' });
      }

      // Build update query
      const updates = {};
      const allowedFields = ['title', 'description', 'category', 'tags'];
      const adminFields = ['is_pinned', 'is_locked'];

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updates[field] = req.body[field];
        }
      });

      // Only admins can update admin fields
      if (req.user.role === 'admin') {
        adminFields.forEach(field => {
          if (req.body[field] !== undefined) {
            updates[field] = req.body[field];
          }
        });
      }

      if (Object.keys(updates).length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
      }

      const fields = Object.keys(updates);
      const values = Object.values(updates);
      const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');

      const updateQuery = `
        UPDATE discussion_threads 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $${fields.length + 1}
        RETURNING *
      `;

      const result = await query(updateQuery, [...values, threadId]);

      res.json({
        message: 'Thread updated successfully',
        thread: result.rows[0]
      });
    } catch (error) {
      console.error('Error updating thread:', error);
      res.status(500).json({ error: 'Failed to update thread' });
    }
  }
);

// Delete thread (only creator or admin)
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const threadId = req.params.id;

    // Check if thread exists and user has permission
    const threadResult = await query(
      'SELECT * FROM discussion_threads WHERE id = $1',
      [threadId]
    );

    if (threadResult.rows.length === 0) {
      return res.status(404).json({ error: 'Thread not found' });
    }

    const thread = threadResult.rows[0];

    // Check permissions
    if (thread.creator_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // Delete thread (cascade will handle comments)
    await query('DELETE FROM discussion_threads WHERE id = $1', [threadId]);

    res.json({ message: 'Thread deleted successfully' });
  } catch (error) {
    console.error('Error deleting thread:', error);
    res.status(500).json({ error: 'Failed to delete thread' });
  }
});

module.exports = router;
