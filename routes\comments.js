// Comments routes
const express = require('express');
const { body, validationResult } = require('express-validator');
const { query, transaction } = require('../lib/database');
const { verifyToken } = require('../middleware/auth');

const router = express.Router();

// Create new comment
router.post('/',
  verifyToken,
  [
    body('thread_id').isUUID().withMessage('Valid thread ID required'),
    body('content').trim().isLength({ min: 1, max: 5000 }).escape(),
    body('parent_comment_id').optional().isUUID()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { thread_id, content, parent_comment_id } = req.body;

      // Verify thread exists and is not locked
      const threadResult = await query(
        'SELECT id, is_locked FROM discussion_threads WHERE id = $1',
        [thread_id]
      );

      if (threadResult.rows.length === 0) {
        return res.status(404).json({ error: 'Thread not found' });
      }

      if (threadResult.rows[0].is_locked) {
        return res.status(403).json({ error: 'Thread is locked' });
      }

      // If replying to a comment, verify it exists and belongs to the thread
      if (parent_comment_id) {
        const parentResult = await query(
          'SELECT id FROM comments WHERE id = $1 AND thread_id = $2 AND is_deleted = false',
          [parent_comment_id, thread_id]
        );

        if (parentResult.rows.length === 0) {
          return res.status(404).json({ error: 'Parent comment not found' });
        }
      }

      // Create comment and update thread reply count in transaction
      const result = await transaction(async (client) => {
        // Insert comment
        const commentResult = await client.query(`
          INSERT INTO comments (thread_id, user_id, content, parent_comment_id)
          VALUES ($1, $2, $3, $4)
          RETURNING *
        `, [thread_id, req.user.id, content, parent_comment_id || null]);

        // Update thread reply count
        await client.query(
          'UPDATE discussion_threads SET reply_count = reply_count + 1 WHERE id = $1',
          [thread_id]
        );

        return commentResult.rows[0];
      });

      // Get comment with user info
      const commentWithUser = await query(`
        SELECT 
          c.*,
          u.username,
          u.role
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.id = $1
      `, [result.id]);

      res.status(201).json({
        message: 'Comment created successfully',
        comment: commentWithUser.rows[0]
      });
    } catch (error) {
      console.error('Error creating comment:', error);
      res.status(500).json({ error: 'Failed to create comment' });
    }
  }
);

// Update comment (only author or admin)
router.put('/:id',
  verifyToken,
  [
    body('content').trim().isLength({ min: 1, max: 5000 }).escape()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const commentId = req.params.id;
      const { content } = req.body;

      // Check if comment exists and user has permission
      const commentResult = await query(
        'SELECT * FROM comments WHERE id = $1 AND is_deleted = false',
        [commentId]
      );

      if (commentResult.rows.length === 0) {
        return res.status(404).json({ error: 'Comment not found' });
      }

      const comment = commentResult.rows[0];

      // Check permissions
      if (comment.user_id !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ error: 'Permission denied' });
      }

      // Update comment
      const updateResult = await query(`
        UPDATE comments 
        SET content = $1, is_edited = true, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `, [content, commentId]);

      // Get updated comment with user info
      const updatedComment = await query(`
        SELECT 
          c.*,
          u.username,
          u.role
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.id = $1
      `, [commentId]);

      res.json({
        message: 'Comment updated successfully',
        comment: updatedComment.rows[0]
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      res.status(500).json({ error: 'Failed to update comment' });
    }
  }
);

// Delete comment (soft delete - only author or admin)
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const commentId = req.params.id;

    // Check if comment exists and user has permission
    const commentResult = await query(
      'SELECT * FROM comments WHERE id = $1 AND is_deleted = false',
      [commentId]
    );

    if (commentResult.rows.length === 0) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    const comment = commentResult.rows[0];

    // Check permissions
    if (comment.user_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // Soft delete comment and update thread reply count
    await transaction(async (client) => {
      // Mark comment as deleted
      await client.query(
        'UPDATE comments SET is_deleted = true, updated_at = NOW() WHERE id = $1',
        [commentId]
      );

      // Update thread reply count
      await client.query(
        'UPDATE discussion_threads SET reply_count = reply_count - 1 WHERE id = $1',
        [comment.thread_id]
      );
    });

    res.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({ error: 'Failed to delete comment' });
  }
});

// Vote on comment
router.post('/:id/vote',
  verifyToken,
  [
    body('vote_type').isIn(['upvote', 'downvote']).withMessage('Vote type must be upvote or downvote')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const commentId = req.params.id;
      const { vote_type } = req.body;

      // Check if comment exists
      const commentResult = await query(
        'SELECT id FROM comments WHERE id = $1 AND is_deleted = false',
        [commentId]
      );

      if (commentResult.rows.length === 0) {
        return res.status(404).json({ error: 'Comment not found' });
      }

      // Handle voting in transaction
      const result = await transaction(async (client) => {
        // Check if user already voted
        const existingVote = await client.query(
          'SELECT vote_type FROM user_votes WHERE user_id = $1 AND comment_id = $2',
          [req.user.id, commentId]
        );

        if (existingVote.rows.length > 0) {
          const currentVote = existingVote.rows[0].vote_type;
          
          if (currentVote === vote_type) {
            // Remove vote if same type
            await client.query(
              'DELETE FROM user_votes WHERE user_id = $1 AND comment_id = $2',
              [req.user.id, commentId]
            );

            // Update comment vote count
            const field = vote_type === 'upvote' ? 'upvotes' : 'downvotes';
            await client.query(
              `UPDATE comments SET ${field} = ${field} - 1 WHERE id = $1`,
              [commentId]
            );

            return { action: 'removed', vote_type };
          } else {
            // Change vote type
            await client.query(
              'UPDATE user_votes SET vote_type = $1 WHERE user_id = $2 AND comment_id = $3',
              [vote_type, req.user.id, commentId]
            );

            // Update comment vote counts
            const oldField = currentVote === 'upvote' ? 'upvotes' : 'downvotes';
            const newField = vote_type === 'upvote' ? 'upvotes' : 'downvotes';
            
            await client.query(
              `UPDATE comments SET ${oldField} = ${oldField} - 1, ${newField} = ${newField} + 1 WHERE id = $1`,
              [commentId]
            );

            return { action: 'changed', vote_type, previous: currentVote };
          }
        } else {
          // Add new vote
          await client.query(
            'INSERT INTO user_votes (user_id, comment_id, vote_type) VALUES ($1, $2, $3)',
            [req.user.id, commentId, vote_type]
          );

          // Update comment vote count
          const field = vote_type === 'upvote' ? 'upvotes' : 'downvotes';
          await client.query(
            `UPDATE comments SET ${field} = ${field} + 1 WHERE id = $1`,
            [commentId]
          );

          return { action: 'added', vote_type };
        }
      });

      // Get updated vote counts
      const updatedComment = await query(
        'SELECT upvotes, downvotes FROM comments WHERE id = $1',
        [commentId]
      );

      res.json({
        message: 'Vote processed successfully',
        result,
        votes: updatedComment.rows[0]
      });
    } catch (error) {
      console.error('Error processing vote:', error);
      res.status(500).json({ error: 'Failed to process vote' });
    }
  }
);

// Get user's vote on a comment
router.get('/:id/vote', verifyToken, async (req, res) => {
  try {
    const commentId = req.params.id;

    const voteResult = await query(
      'SELECT vote_type FROM user_votes WHERE user_id = $1 AND comment_id = $2',
      [req.user.id, commentId]
    );

    res.json({
      vote: voteResult.rows.length > 0 ? voteResult.rows[0].vote_type : null
    });
  } catch (error) {
    console.error('Error getting user vote:', error);
    res.status(500).json({ error: 'Failed to get vote' });
  }
});

module.exports = router;
