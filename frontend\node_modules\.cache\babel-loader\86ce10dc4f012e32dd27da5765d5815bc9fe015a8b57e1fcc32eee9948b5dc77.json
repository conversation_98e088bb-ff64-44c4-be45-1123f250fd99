{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const {\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) {\n        setError(error.message);\n      } else {\n        navigate('/');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n        children: \"Sign in to your account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-center text-sm text-gray-600\",\n        children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          className: \"font-medium text-blue-600 hover:text-blue-500\",\n          children: \"create a new account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"space-y-6\",\n          onSubmit: handleSubmit,\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                required: true,\n                value: email,\n                onChange: e => setEmail(e.target.value),\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                autoComplete: \"current-password\",\n                required: true,\n                value: password,\n                onChange: e => setPassword(e.target.value),\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Enter your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"remember-me\",\n                name: \"remember-me\",\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember-me\",\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"font-medium text-blue-600 hover:text-blue-500\",\n                children: \"Forgot your password?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: loading ? 'Signing in...' : 'Sign in'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full border-t border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex justify-center text-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-2 bg-white text-gray-500\",\n                children: \"Or continue with\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 grid grid-cols-2 gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2\",\n                children: \"Google\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2\",\n                children: \"Twitter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"oys+aRSDi2Lrf1KkdpCiCHhej3A=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "supabase", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "loading", "setLoading", "error", "setError", "navigate", "handleSubmit", "e", "preventDefault", "auth", "signInWithPassword", "message", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "htmlFor", "id", "name", "type", "autoComplete", "required", "value", "onChange", "target", "placeholder", "disabled", "viewBox", "fill", "d", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\n\nconst Login = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        setError(error.message);\n      } else {\n        navigate('/');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n          Sign in to your account\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <Link\n            to=\"/register\"\n            className=\"font-medium text-blue-600 hover:text-blue-500\"\n          >\n            create a new account\n          </Link>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your password\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"remember-me\"\n                  name=\"remember-me\"\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n                  Remember me\n                </label>\n              </div>\n\n              <div className=\"text-sm\">\n                <button type=\"button\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n                  Forgot your password?\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Signing in...' : 'Sign in'}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-2 gap-3\">\n              <button\n                type=\"button\"\n                className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\"\n              >\n                <svg className=\"h-5 w-5\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n                <span className=\"ml-2\">Google</span>\n              </button>\n\n              <button\n                type=\"button\"\n                className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\"\n              >\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                </svg>\n                <span className=\"ml-2\">Twitter</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMgB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBN,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM;QAAED;MAAM,CAAC,GAAG,MAAMX,QAAQ,CAACiB,IAAI,CAACC,kBAAkB,CAAC;QACvDb,KAAK;QACLE;MACF,CAAC,CAAC;MAEF,IAAII,KAAK,EAAE;QACTC,QAAQ,CAACD,KAAK,CAACQ,OAAO,CAAC;MACzB,CAAC,MAAM;QACLN,QAAQ,CAAC,GAAG,CAAC;MACf;IACF,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZR,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA;IAAKmB,SAAS,EAAC,4EAA4E;IAAAC,QAAA,gBACzFpB,OAAA;MAAKmB,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CpB,OAAA;QAAImB,SAAS,EAAC,wDAAwD;QAAAC,QAAA,EAAC;MAEvE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLxB,OAAA;QAAGmB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,GAAC,IAClD,EAAC,GAAG,eACNpB,OAAA,CAACJ,IAAI;UACH6B,EAAE,EAAC,WAAW;UACdN,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAC1D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENxB,OAAA;MAAKmB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDpB,OAAA;QAAKmB,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DpB,OAAA;UAAMmB,SAAS,EAAC,WAAW;UAACO,QAAQ,EAAEd,YAAa;UAAAQ,QAAA,GAChDX,KAAK,iBACJT,OAAA;YAAKmB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAC/EX;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDxB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAO2B,OAAO,EAAC,OAAO;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxB,OAAA;cAAKmB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBpB,OAAA;gBACE4B,EAAE,EAAC,OAAO;gBACVC,IAAI,EAAC,OAAO;gBACZC,IAAI,EAAC,OAAO;gBACZC,YAAY,EAAC,OAAO;gBACpBC,QAAQ;gBACRC,KAAK,EAAE9B,KAAM;gBACb+B,QAAQ,EAAGrB,CAAC,IAAKT,QAAQ,CAACS,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;gBAC1Cd,SAAS,EAAC,uKAAuK;gBACjLiB,WAAW,EAAC;cAAkB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAO2B,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxB,OAAA;cAAKmB,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBpB,OAAA;gBACE4B,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAC,UAAU;gBACfC,IAAI,EAAC,UAAU;gBACfC,YAAY,EAAC,kBAAkB;gBAC/BC,QAAQ;gBACRC,KAAK,EAAE5B,QAAS;gBAChB6B,QAAQ,EAAGrB,CAAC,IAAKP,WAAW,CAACO,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;gBAC7Cd,SAAS,EAAC,uKAAuK;gBACjLiB,WAAW,EAAC;cAAqB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAKmB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpB,OAAA;cAAKmB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCpB,OAAA;gBACE4B,EAAE,EAAC,aAAa;gBAChBC,IAAI,EAAC,aAAa;gBAClBC,IAAI,EAAC,UAAU;gBACfX,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACFxB,OAAA;gBAAO2B,OAAO,EAAC,aAAa;gBAACR,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAE1E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENxB,OAAA;cAAKmB,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtBpB,OAAA;gBAAQ8B,IAAI,EAAC,QAAQ;gBAACX,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAAoB,QAAA,eACEpB,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbO,QAAQ,EAAE9B,OAAQ;cAClBY,SAAS,EAAC,0QAA0Q;cAAAC,QAAA,EAEnRb,OAAO,GAAG,eAAe,GAAG;YAAS;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPxB,OAAA;UAAKmB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpB,OAAA;YAAKmB,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBpB,OAAA;cAAKmB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDpB,OAAA;gBAAKmB,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNxB,OAAA;cAAKmB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,eACnDpB,OAAA;gBAAMmB,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxB,OAAA;YAAKmB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpB,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbX,SAAS,EAAC,qJAAqJ;cAAAC,QAAA,gBAE/JpB,OAAA;gBAAKmB,SAAS,EAAC,SAAS;gBAACmB,OAAO,EAAC,WAAW;gBAAAlB,QAAA,gBAC1CpB,OAAA;kBACEuC,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAyH;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5H,CAAC,eACFxB,OAAA;kBACEuC,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAuI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CAAC,eACFxB,OAAA;kBACEuC,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAA+H;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClI,CAAC,eACFxB,OAAA;kBACEuC,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAqI;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxB,OAAA;gBAAMmB,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAETxB,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbX,SAAS,EAAC,qJAAqJ;cAAAC,QAAA,gBAE/JpB,OAAA;gBAAKmB,SAAS,EAAC,SAAS;gBAACoB,IAAI,EAAC,cAAc;gBAACD,OAAO,EAAC,WAAW;gBAAAlB,QAAA,eAC9DpB,OAAA;kBAAMwC,CAAC,EAAC;gBAA4f;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACngB,CAAC,eACNxB,OAAA;gBAAMmB,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAhLID,KAAK;EAAA,QAKQJ,WAAW;AAAA;AAAA4C,EAAA,GALxBxC,KAAK;AAkLX,eAAeA,KAAK;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}