import React, { createContext, useContext } from 'react';
import { useAuth } from './AuthContext';

const ApiContext = createContext();

export const useApi = () => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

export const ApiProvider = ({ children }) => {
  const { token } = useAuth();
  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

  // Generic API call function
  const apiCall = async (endpoint, options = {}) => {
    const url = `${API_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (response.ok) {
        return { success: true, data };
      } else {
        return { success: false, error: data.error || 'API call failed' };
      }
    } catch (error) {
      console.error('API call error:', error);
      return { success: false, error: 'Network error occurred' };
    }
  };

  // Articles API
  const articles = {
    getAll: (params = {}) => {
      const queryString = new URLSearchParams(params).toString();
      return apiCall(`/api/articles${queryString ? `?${queryString}` : ''}`);
    },

    getBySlug: (slug) => apiCall(`/api/articles/${slug}`),

    create: (articleData) => apiCall('/api/articles', {
      method: 'POST',
      body: JSON.stringify(articleData)
    }),

    update: (slug, updates) => apiCall(`/api/articles/${slug}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    }),

    delete: (slug) => apiCall(`/api/articles/${slug}`, {
      method: 'DELETE'
    })
  };

  // Discussions API
  const discussions = {
    getAll: (params = {}) => {
      const queryString = new URLSearchParams(params).toString();
      return apiCall(`/api/discussions${queryString ? `?${queryString}` : ''}`);
    },

    getById: (id) => apiCall(`/api/discussions/${id}`),

    create: (threadData) => apiCall('/api/discussions', {
      method: 'POST',
      body: JSON.stringify(threadData)
    }),

    update: (id, updates) => apiCall(`/api/discussions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    }),

    delete: (id) => apiCall(`/api/discussions/${id}`, {
      method: 'DELETE'
    })
  };

  // Comments API
  const comments = {
    create: (commentData) => apiCall('/api/comments', {
      method: 'POST',
      body: JSON.stringify(commentData)
    }),

    update: (id, updates) => apiCall(`/api/comments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    }),

    delete: (id) => apiCall(`/api/comments/${id}`, {
      method: 'DELETE'
    }),

    vote: (id, voteType) => apiCall(`/api/comments/${id}/vote`, {
      method: 'POST',
      body: JSON.stringify({ vote_type: voteType })
    }),

    getUserVote: (id) => apiCall(`/api/comments/${id}/vote`)
  };

  // Health check
  const health = {
    checkDatabase: () => apiCall('/health/db')
  };

  const value = {
    apiCall,
    articles,
    discussions,
    comments,
    health
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};
