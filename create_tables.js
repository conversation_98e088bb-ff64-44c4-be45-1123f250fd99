// Simple table creation script for Politica
require('dotenv').config();
const { Pool } = require('pg');

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'POLITICA_AUG',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'Rayvical',
});

async function createTables() {
  console.log('🔧 Creating tables...');
  
  try {
    // Enable UUID extension
    await pool.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
    console.log('✅ UUID extension enabled');

    // Create users table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(20) DEFAULT 'regular' CHECK (role IN ('regular', 'verified', 'admin')),
        verified_status BOOLEAN DEFAULT FALSE,
        bio TEXT,
        avatar_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ Users table created');

    // Create knowledge_articles table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS knowledge_articles (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        content_level_1 TEXT NOT NULL,
        content_level_2 TEXT,
        content_level_3 TEXT,
        author_id UUID REFERENCES users(id) ON DELETE CASCADE,
        category VARCHAR(100) NOT NULL,
        tags TEXT[],
        is_published BOOLEAN DEFAULT FALSE,
        view_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ Knowledge articles table created');

    // Create discussion_threads table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS discussion_threads (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        creator_id UUID REFERENCES users(id) ON DELETE CASCADE,
        category VARCHAR(100) NOT NULL,
        tags TEXT[],
        is_pinned BOOLEAN DEFAULT FALSE,
        is_locked BOOLEAN DEFAULT FALSE,
        view_count INTEGER DEFAULT 0,
        reply_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ Discussion threads table created');

    // Create comments table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS comments (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        thread_id UUID REFERENCES discussion_threads(id) ON DELETE CASCADE,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        content TEXT NOT NULL,
        parent_comment_id UUID REFERENCES comments(id) ON DELETE CASCADE,
        is_edited BOOLEAN DEFAULT FALSE,
        is_deleted BOOLEAN DEFAULT FALSE,
        upvotes INTEGER DEFAULT 0,
        downvotes INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ Comments table created');

    // Create research_papers table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS research_papers (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        abstract TEXT NOT NULL,
        content TEXT,
        file_url TEXT,
        author_id UUID REFERENCES users(id) ON DELETE CASCADE,
        co_authors TEXT[],
        keywords TEXT[],
        category VARCHAR(100) NOT NULL,
        status VARCHAR(20) DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'accepted', 'rejected', 'published')),
        reviewer_notes TEXT,
        doi VARCHAR(255),
        citation_count INTEGER DEFAULT 0,
        download_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ Research papers table created');

    // Create live_debates table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS live_debates (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        topic VARCHAR(255) NOT NULL,
        moderator_id UUID REFERENCES users(id) ON DELETE SET NULL,
        participants UUID[],
        scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
        duration_minutes INTEGER DEFAULT 60,
        status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'completed', 'cancelled')),
        stream_url TEXT,
        chat_enabled BOOLEAN DEFAULT TRUE,
        polls_enabled BOOLEAN DEFAULT TRUE,
        max_participants INTEGER DEFAULT 10,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ Live debates table created');

    // Create verification_requests table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS verification_requests (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        credentials TEXT NOT NULL,
        supporting_documents TEXT[],
        status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
        admin_notes TEXT,
        reviewed_by UUID REFERENCES users(id) ON DELETE SET NULL,
        reviewed_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ Verification requests table created');

    // Create user_votes table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_votes (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        comment_id UUID REFERENCES comments(id) ON DELETE CASCADE,
        vote_type VARCHAR(10) CHECK (vote_type IN ('upvote', 'downvote')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, comment_id)
      )
    `);
    console.log('✅ User votes table created');

    // Create user_sessions table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);
    console.log('✅ User sessions table created');

    console.log('\n🎉 All tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    throw error;
  }
}

async function createIndexes() {
  console.log('\n🔍 Creating indexes...');
  
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_knowledge_articles_author ON knowledge_articles(author_id)',
    'CREATE INDEX IF NOT EXISTS idx_knowledge_articles_category ON knowledge_articles(category)',
    'CREATE INDEX IF NOT EXISTS idx_knowledge_articles_published ON knowledge_articles(is_published)',
    'CREATE INDEX IF NOT EXISTS idx_discussion_threads_creator ON discussion_threads(creator_id)',
    'CREATE INDEX IF NOT EXISTS idx_comments_thread ON comments(thread_id)',
    'CREATE INDEX IF NOT EXISTS idx_comments_user ON comments(user_id)',
    'CREATE INDEX IF NOT EXISTS idx_research_papers_author ON research_papers(author_id)',
    'CREATE INDEX IF NOT EXISTS idx_research_papers_status ON research_papers(status)',
    'CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)',
    'CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id)'
  ];

  for (const indexSQL of indexes) {
    try {
      await pool.query(indexSQL);
    } catch (error) {
      console.error('Error creating index:', error.message);
    }
  }
  
  console.log('✅ Indexes created');
}

async function insertSampleData() {
  console.log('\n🌱 Inserting sample data...');
  
  try {
    // Insert sample users with hashed passwords (bcrypt hash of 'password123')
    const hashedPassword = '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PmO.S6'; // password123
    
    await pool.query(`
      INSERT INTO users (username, email, password_hash, role, verified_status, bio) 
      VALUES 
        ('admin', '<EMAIL>', $1, 'admin', true, 'Platform administrator and political science expert.'),
        ('dr_johnson', '<EMAIL>', $1, 'verified', true, 'Political Science Professor specializing in democratic institutions.'),
        ('policy_expert', '<EMAIL>', $1, 'verified', true, 'Policy analyst with expertise in comparative political systems.'),
        ('student_pol', '<EMAIL>', $1, 'regular', false, 'Political science student interested in learning about democratic processes.')
      ON CONFLICT (email) DO NOTHING
    `, [hashedPassword]);
    
    console.log('✅ Sample users inserted');

    // Get user IDs for foreign keys
    const adminUser = await pool.query("SELECT id FROM users WHERE username = 'admin' LIMIT 1");
    const drJohnsonUser = await pool.query("SELECT id FROM users WHERE username = 'dr_johnson' LIMIT 1");
    
    if (adminUser.rows.length > 0 && drJohnsonUser.rows.length > 0) {
      // Insert sample articles
      await pool.query(`
        INSERT INTO knowledge_articles (title, slug, content_level_1, content_level_2, content_level_3, author_id, category, tags, is_published)
        VALUES 
          ('Understanding Democracy', 'understanding-democracy', 
           'Democracy is a form of government where power is held by the people, either directly or through elected representatives.',
           'Democracy encompasses various forms including direct democracy and representative democracy. Key principles include majority rule with minority rights, individual freedoms, and regular elections.',
           'Democratic theory involves complex concepts like the democratic peace theory, the role of civil society, checks and balances, and the tension between majority rule and individual rights.',
           $1, 'Political Systems', ARRAY['democracy', 'government', 'voting'], true),
          ('The Electoral College System', 'electoral-college-system',
           'The Electoral College is the system used in the United States to elect the President and Vice President.',
           'Each state gets electoral votes equal to its total number of senators and representatives in Congress. Most states use a winner-take-all system.',
           'The Electoral College was designed as a compromise between direct popular vote and congressional selection, reflecting concerns about federalism and the balance between large and small states.',
           $2, 'Elections', ARRAY['electoral college', 'elections', 'presidency'], true)
        ON CONFLICT (slug) DO NOTHING
      `, [drJohnsonUser.rows[0].id, drJohnsonUser.rows[0].id]);
      
      console.log('✅ Sample articles inserted');
    }

    console.log('✅ Sample data insertion completed');

  } catch (error) {
    console.error('❌ Error inserting sample data:', error.message);
    // Don't throw error for sample data - it's not critical
  }
}

async function verifySetup() {
  console.log('\n🔍 Verifying setup...');
  
  try {
    const tables = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('📋 Tables created:');
    tables.rows.forEach(row => console.log(`   - ${row.table_name}`));
    
    const userCount = await pool.query('SELECT COUNT(*) FROM users');
    const articleCount = await pool.query('SELECT COUNT(*) FROM knowledge_articles');
    
    console.log('\n📊 Data counts:');
    console.log(`   - Users: ${userCount.rows[0].count}`);
    console.log(`   - Articles: ${articleCount.rows[0].count}`);
    
    console.log('\n✅ Database verification completed successfully!');
    
  } catch (error) {
    console.error('❌ Error verifying setup:', error.message);
    throw error;
  }
}

async function main() {
  console.log('🚀 Setting up Politica Database');
  console.log('=' .repeat(50));
  
  try {
    await createTables();
    await createIndexes();
    await insertSampleData();
    await verifySetup();
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('You can now start the server with: npm start');
    
  } catch (error) {
    console.error('\n💥 Setup failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}

module.exports = { createTables, insertSampleData };
