// Comprehensive API test for Politica platform
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data
const testUser = {
  username: 'api_tester_' + Date.now(),
  email: `tester${Date.now()}@example.com`,
  password: 'TestPassword123'
};

let authToken = '';
let createdThreadId = '';
let createdCommentId = '';
let createdArticleSlug = '';

// Helper function for API calls
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// Test functions
async function testBasicEndpoints() {
  console.log('\n🔍 Testing Basic Endpoints...');
  
  try {
    // Health check
    const health = await api.get('/health/db');
    console.log('✅ Health check:', health.data.message);

    // Get articles
    const articles = await api.get('/api/articles');
    console.log(`✅ Articles: ${articles.data.data.length} found`);

    return true;
  } catch (error) {
    console.error('❌ Basic endpoints failed:', error.message);
    return false;
  }
}

async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');
  
  try {
    // Register user
    const registerResponse = await api.post('/api/auth/register', testUser);
    console.log('✅ User registered:', registerResponse.data.user.username);
    authToken = registerResponse.data.token;

    // Login
    const loginResponse = await api.post('/api/auth/login', {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login successful:', loginResponse.data.user.username);
    authToken = loginResponse.data.token;

    // Get profile
    const profileResponse = await api.get('/api/auth/profile');
    console.log('✅ Profile retrieved:', profileResponse.data.user.username);

    return true;
  } catch (error) {
    console.error('❌ Authentication failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testDiscussionThreads() {
  console.log('\n💬 Testing Discussion Threads...');
  
  try {
    // Create thread
    const threadData = {
      title: 'Test Discussion Thread',
      description: 'This is a test thread created by the API test script.',
      category: 'Testing',
      tags: ['test', 'api', 'automation']
    };

    const createResponse = await api.post('/api/discussions', threadData);
    console.log('✅ Thread created:', createResponse.data.thread.title);
    createdThreadId = createResponse.data.thread.id;

    // Get all threads
    const threadsResponse = await api.get('/api/discussions');
    console.log(`✅ Threads retrieved: ${threadsResponse.data.data.length} found`);

    // Get single thread
    const singleThreadResponse = await api.get(`/api/discussions/${createdThreadId}`);
    console.log('✅ Single thread retrieved:', singleThreadResponse.data.thread.title);

    // Update thread
    const updateResponse = await api.put(`/api/discussions/${createdThreadId}`, {
      description: 'Updated description for the test thread.'
    });
    console.log('✅ Thread updated successfully');

    return true;
  } catch (error) {
    console.error('❌ Discussion threads failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testComments() {
  console.log('\n💭 Testing Comments...');
  
  try {
    if (!createdThreadId) {
      console.log('⚠️ Skipping comments test - no thread created');
      return false;
    }

    // Create comment
    const commentData = {
      thread_id: createdThreadId,
      content: 'This is a test comment created by the API test script.'
    };

    const createResponse = await api.post('/api/comments', commentData);
    console.log('✅ Comment created:', createResponse.data.comment.content.substring(0, 50) + '...');
    createdCommentId = createResponse.data.comment.id;

    // Update comment
    const updateResponse = await api.put(`/api/comments/${createdCommentId}`, {
      content: 'This is an updated test comment.'
    });
    console.log('✅ Comment updated successfully');

    // Vote on comment
    const voteResponse = await api.post(`/api/comments/${createdCommentId}/vote`, {
      vote_type: 'upvote'
    });
    console.log('✅ Comment voted:', voteResponse.data.result.action);

    // Get user's vote
    const userVoteResponse = await api.get(`/api/comments/${createdCommentId}/vote`);
    console.log('✅ User vote retrieved:', userVoteResponse.data.vote);

    return true;
  } catch (error) {
    console.error('❌ Comments failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testKnowledgeArticles() {
  console.log('\n📚 Testing Knowledge Articles...');
  
  try {
    // First, make user verified (simulate admin action)
    // For testing, we'll try to create an article and handle the verification error
    
    const articleData = {
      title: 'Test Knowledge Article',
      content_level_1: 'This is the basic level content for the test article. It provides fundamental information about the topic.',
      content_level_2: 'This is the intermediate level content that goes deeper into the subject matter.',
      content_level_3: 'This is the advanced level content for experts and researchers.',
      category: 'Testing',
      tags: ['test', 'knowledge', 'api'],
      is_published: false
    };

    try {
      const createResponse = await api.post('/api/articles', articleData);
      console.log('✅ Article created:', createResponse.data.article.title);
      createdArticleSlug = createResponse.data.article.slug;

      // Get article by slug
      const articleResponse = await api.get(`/api/articles/${createdArticleSlug}`);
      console.log('✅ Article retrieved:', articleResponse.data.article.title);

      // Update article
      const updateResponse = await api.put(`/api/articles/${createdArticleSlug}`, {
        title: 'Updated Test Knowledge Article'
      });
      console.log('✅ Article updated successfully');

      return true;
    } catch (error) {
      if (error.response?.data?.error === 'Verified status required') {
        console.log('ℹ️ Article creation requires verified status (expected for regular users)');
        return true; // This is expected behavior
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('❌ Knowledge articles failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testSearchAndFiltering() {
  console.log('\n🔍 Testing Search and Filtering...');
  
  try {
    // Search articles
    const searchResponse = await api.get('/api/articles?search=democracy');
    console.log(`✅ Article search: ${searchResponse.data.data.length} results for "democracy"`);

    // Filter by category
    const categoryResponse = await api.get('/api/articles?category=Political Systems');
    console.log(`✅ Category filter: ${categoryResponse.data.data.length} articles in "Political Systems"`);

    // Pagination
    const paginationResponse = await api.get('/api/articles?page=1&limit=1');
    console.log(`✅ Pagination: Page 1 with limit 1, total: ${paginationResponse.data.pagination.total}`);

    // Search discussions
    const discussionSearchResponse = await api.get('/api/discussions?search=test');
    console.log(`✅ Discussion search: ${discussionSearchResponse.data.data.length} results for "test"`);

    return true;
  } catch (error) {
    console.error('❌ Search and filtering failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testErrorHandling() {
  console.log('\n🚫 Testing Error Handling...');
  
  try {
    let errorTests = 0;
    let passedTests = 0;

    // Test 404 for non-existent thread
    try {
      await api.get('/api/discussions/00000000-0000-0000-0000-000000000000');
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ 404 error handled correctly for non-existent thread');
        passedTests++;
      }
      errorTests++;
    }

    // Test 404 for non-existent article
    try {
      await api.get('/api/articles/non-existent-article');
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ 404 error handled correctly for non-existent article');
        passedTests++;
      }
      errorTests++;
    }

    // Test validation error
    try {
      await api.post('/api/discussions', { title: 'x' }); // Too short title
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Validation error handled correctly');
        passedTests++;
      }
      errorTests++;
    }

    console.log(`✅ Error handling: ${passedTests}/${errorTests} tests passed`);
    return passedTests === errorTests;
  } catch (error) {
    console.error('❌ Error handling tests failed:', error.message);
    return false;
  }
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    // Delete created comment
    if (createdCommentId) {
      await api.delete(`/api/comments/${createdCommentId}`);
      console.log('✅ Test comment deleted');
    }

    // Delete created thread
    if (createdThreadId) {
      await api.delete(`/api/discussions/${createdThreadId}`);
      console.log('✅ Test thread deleted');
    }

    // Delete created article
    if (createdArticleSlug) {
      await api.delete(`/api/articles/${createdArticleSlug}`);
      console.log('✅ Test article deleted');
    }

    return true;
  } catch (error) {
    console.error('⚠️ Cleanup failed (this is usually okay):', error.response?.data?.error || error.message);
    return true; // Don't fail the overall test for cleanup issues
  }
}

// Main test runner
async function runFullAPITests() {
  console.log('🚀 Starting Comprehensive Politica API Tests');
  console.log('=' .repeat(60));
  
  const tests = [
    { name: 'Basic Endpoints', fn: testBasicEndpoints },
    { name: 'Authentication', fn: testAuthentication },
    { name: 'Discussion Threads', fn: testDiscussionThreads },
    { name: 'Comments', fn: testComments },
    { name: 'Knowledge Articles', fn: testKnowledgeArticles },
    { name: 'Search and Filtering', fn: testSearchAndFiltering },
    { name: 'Error Handling', fn: testErrorHandling },
    { name: 'Cleanup', fn: cleanup }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" threw an error:`, error.message);
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Comprehensive Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! The Politica API is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the output above for details.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runFullAPITests().catch(error => {
    console.error('💥 Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runFullAPITests };
