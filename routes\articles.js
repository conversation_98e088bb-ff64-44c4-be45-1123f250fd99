// Knowledge articles routes
const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../lib/database');
const { verifyToken, requireVerified, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
}

// Get all published articles with pagination and filtering
router.get('/', optionalAuth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const category = req.query.category;
    const search = req.query.search;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE a.is_published = true';
    let params = [];
    let paramCount = 1;

    // Add category filter
    if (category) {
      whereClause += ` AND a.category = $${paramCount}`;
      params.push(category);
      paramCount++;
    }

    // Add search filter
    if (search) {
      whereClause += ` AND (a.title ILIKE $${paramCount} OR a.content_level_1 ILIKE $${paramCount})`;
      params.push(`%${search}%`);
      paramCount++;
    }

    // For non-verified users, only show published articles
    // For verified users and admins, show all articles they have access to
    if (req.user && (req.user.verified_status || req.user.role === 'admin')) {
      whereClause = whereClause.replace('a.is_published = true', '(a.is_published = true OR a.author_id = $' + paramCount + ')');
      params.push(req.user.id);
      paramCount++;
    }

    const articlesQuery = `
      SELECT 
        a.id,
        a.title,
        a.slug,
        a.content_level_1,
        a.category,
        a.tags,
        a.is_published,
        a.view_count,
        a.created_at,
        a.updated_at,
        u.username as author_name,
        u.role as author_role
      FROM knowledge_articles a
      LEFT JOIN users u ON a.author_id = u.id
      ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `;

    params.push(limit, offset);

    const countQuery = `
      SELECT COUNT(*) as total
      FROM knowledge_articles a
      ${whereClause}
    `;

    const [articlesResult, countResult] = await Promise.all([
      query(articlesQuery, params),
      query(countQuery, params.slice(0, -2)) // Remove limit and offset for count
    ]);

    const totalArticles = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalArticles / limit);

    // Get unique categories for filtering
    const categoriesResult = await query(`
      SELECT DISTINCT category 
      FROM knowledge_articles 
      WHERE is_published = true 
      ORDER BY category
    `);

    res.json({
      data: articlesResult.rows,
      pagination: {
        page,
        limit,
        total: totalArticles,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      categories: categoriesResult.rows.map(row => row.category)
    });
  } catch (error) {
    console.error('Error fetching articles:', error);
    res.status(500).json({ error: 'Failed to fetch articles' });
  }
});

// Get single article by slug
router.get('/:slug', optionalAuth, async (req, res) => {
  try {
    const slug = req.params.slug;

    const articleQuery = `
      SELECT 
        a.*,
        u.username as author_name,
        u.role as author_role
      FROM knowledge_articles a
      LEFT JOIN users u ON a.author_id = u.id
      WHERE a.slug = $1
    `;

    const result = await query(articleQuery, [slug]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Article not found' });
    }

    const article = result.rows[0];

    // Check if user has permission to view unpublished articles
    if (!article.is_published) {
      if (!req.user || (req.user.id !== article.author_id && req.user.role !== 'admin')) {
        return res.status(404).json({ error: 'Article not found' });
      }
    }

    // Update view count
    await query(
      'UPDATE knowledge_articles SET view_count = view_count + 1 WHERE id = $1',
      [article.id]
    );

    res.json({
      article: {
        ...article,
        view_count: article.view_count + 1
      }
    });
  } catch (error) {
    console.error('Error fetching article:', error);
    res.status(500).json({ error: 'Failed to fetch article' });
  }
});

// Create new article (verified users only)
router.post('/',
  verifyToken,
  requireVerified,
  [
    body('title').trim().isLength({ min: 5, max: 255 }).escape(),
    body('content_level_1').trim().isLength({ min: 50 }).escape(),
    body('content_level_2').optional().trim().escape(),
    body('content_level_3').optional().trim().escape(),
    body('category').trim().isLength({ min: 1, max: 100 }).escape(),
    body('tags').optional().isArray({ max: 10 }),
    body('is_published').optional().isBoolean()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { title, content_level_1, content_level_2, content_level_3, category, tags, is_published } = req.body;

      // Generate slug from title
      let slug = generateSlug(title);
      
      // Check if slug already exists and make it unique
      let slugExists = true;
      let counter = 1;
      let finalSlug = slug;

      while (slugExists) {
        const slugCheck = await query(
          'SELECT id FROM knowledge_articles WHERE slug = $1',
          [finalSlug]
        );

        if (slugCheck.rows.length === 0) {
          slugExists = false;
        } else {
          finalSlug = `${slug}-${counter}`;
          counter++;
        }
      }

      const insertQuery = `
        INSERT INTO knowledge_articles (
          title, slug, content_level_1, content_level_2, content_level_3, 
          author_id, category, tags, is_published
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `;

      const result = await query(insertQuery, [
        title,
        finalSlug,
        content_level_1,
        content_level_2 || null,
        content_level_3 || null,
        req.user.id,
        category,
        tags || [],
        is_published || false
      ]);

      const newArticle = result.rows[0];

      res.status(201).json({
        message: 'Article created successfully',
        article: {
          ...newArticle,
          author_name: req.user.username,
          author_role: req.user.role
        }
      });
    } catch (error) {
      console.error('Error creating article:', error);
      res.status(500).json({ error: 'Failed to create article' });
    }
  }
);

// Update article (only author or admin)
router.put('/:slug',
  verifyToken,
  [
    body('title').optional().trim().isLength({ min: 5, max: 255 }).escape(),
    body('content_level_1').optional().trim().isLength({ min: 50 }).escape(),
    body('content_level_2').optional().trim().escape(),
    body('content_level_3').optional().trim().escape(),
    body('category').optional().trim().isLength({ min: 1, max: 100 }).escape(),
    body('tags').optional().isArray({ max: 10 }),
    body('is_published').optional().isBoolean()
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const slug = req.params.slug;

      // Check if article exists and user has permission
      const articleResult = await query(
        'SELECT * FROM knowledge_articles WHERE slug = $1',
        [slug]
      );

      if (articleResult.rows.length === 0) {
        return res.status(404).json({ error: 'Article not found' });
      }

      const article = articleResult.rows[0];

      // Check permissions
      if (article.author_id !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({ error: 'Permission denied' });
      }

      // Build update query
      const updates = {};
      const allowedFields = ['title', 'content_level_1', 'content_level_2', 'content_level_3', 'category', 'tags', 'is_published'];

      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updates[field] = req.body[field];
        }
      });

      // If title is being updated, update slug too
      if (updates.title && updates.title !== article.title) {
        let newSlug = generateSlug(updates.title);
        
        // Check if new slug already exists
        const slugCheck = await query(
          'SELECT id FROM knowledge_articles WHERE slug = $1 AND id != $2',
          [newSlug, article.id]
        );

        if (slugCheck.rows.length > 0) {
          newSlug = `${newSlug}-${Date.now()}`;
        }

        updates.slug = newSlug;
      }

      if (Object.keys(updates).length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
      }

      const fields = Object.keys(updates);
      const values = Object.values(updates);
      const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');

      const updateQuery = `
        UPDATE knowledge_articles 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $${fields.length + 1}
        RETURNING *
      `;

      const result = await query(updateQuery, [...values, article.id]);

      res.json({
        message: 'Article updated successfully',
        article: result.rows[0]
      });
    } catch (error) {
      console.error('Error updating article:', error);
      res.status(500).json({ error: 'Failed to update article' });
    }
  }
);

// Delete article (only author or admin)
router.delete('/:slug', verifyToken, async (req, res) => {
  try {
    const slug = req.params.slug;

    // Check if article exists and user has permission
    const articleResult = await query(
      'SELECT * FROM knowledge_articles WHERE slug = $1',
      [slug]
    );

    if (articleResult.rows.length === 0) {
      return res.status(404).json({ error: 'Article not found' });
    }

    const article = articleResult.rows[0];

    // Check permissions
    if (article.author_id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // Delete article
    await query('DELETE FROM knowledge_articles WHERE id = $1', [article.id]);

    res.json({ message: 'Article deleted successfully' });
  } catch (error) {
    console.error('Error deleting article:', error);
    res.status(500).json({ error: 'Failed to delete article' });
  }
});

module.exports = router;
