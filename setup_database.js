// Database setup script for Politica
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection for initial setup (connect to postgres database first)
const setupPool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: 'postgres', // Connect to default postgres database first
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'Rayvical',
});

// Database connection for the target database
const targetPool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'POLITICA_AUG',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'Rayvical',
});

async function createDatabase() {
  console.log('🔧 Creating database...');
  try {
    // Check if database exists
    const checkResult = await setupPool.query(
      "SELECT 1 FROM pg_database WHERE datname = $1",
      [process.env.DB_NAME || 'POLITICA_AUG']
    );

    if (checkResult.rows.length === 0) {
      // Create database
      await setupPool.query(`CREATE DATABASE "${process.env.DB_NAME || 'POLITICA_AUG'}"`);
      console.log('✅ Database created successfully');
    } else {
      console.log('ℹ️ Database already exists');
    }
  } catch (error) {
    if (error.code === '42P04') {
      console.log('ℹ️ Database already exists');
    } else {
      console.error('❌ Error creating database:', error.message);
      throw error;
    }
  }
}

async function runSchemaScript() {
  console.log('📋 Running schema script...');
  try {
    const schemaPath = path.join(__dirname, 'database', 'postgresql_setup.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // Split by semicolon and filter out empty statements
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('\\c'));

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await targetPool.query(statement);
        } catch (error) {
          // Ignore "already exists" errors
          if (!error.message.includes('already exists')) {
            console.error('Error executing statement:', statement.substring(0, 100) + '...');
            console.error('Error:', error.message);
          }
        }
      }
    }
    
    console.log('✅ Schema created successfully');
  } catch (error) {
    console.error('❌ Error running schema script:', error.message);
    throw error;
  }
}

async function runSeedScript() {
  console.log('🌱 Running seed script...');
  try {
    const seedPath = path.join(__dirname, 'database', 'postgresql_seed_data.sql');
    const seedSQL = fs.readFileSync(seedPath, 'utf8');
    
    // Split by semicolon and filter out empty statements
    const statements = seedSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('\\c'));

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await targetPool.query(statement);
        } catch (error) {
          // Ignore duplicate key errors for seed data
          if (!error.message.includes('duplicate key') && !error.message.includes('already exists')) {
            console.error('Error executing statement:', statement.substring(0, 100) + '...');
            console.error('Error:', error.message);
          }
        }
      }
    }
    
    console.log('✅ Seed data inserted successfully');
  } catch (error) {
    console.error('❌ Error running seed script:', error.message);
    throw error;
  }
}

async function verifySetup() {
  console.log('🔍 Verifying database setup...');
  try {
    // Check tables exist
    const tablesResult = await targetPool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log('📋 Tables created:');
    tablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });
    
    // Check sample data
    const usersCount = await targetPool.query('SELECT COUNT(*) FROM users');
    const articlesCount = await targetPool.query('SELECT COUNT(*) FROM knowledge_articles');
    const threadsCount = await targetPool.query('SELECT COUNT(*) FROM discussion_threads');
    
    console.log('📊 Sample data:');
    console.log(`   - Users: ${usersCount.rows[0].count}`);
    console.log(`   - Articles: ${articlesCount.rows[0].count}`);
    console.log(`   - Discussion threads: ${threadsCount.rows[0].count}`);
    
    console.log('✅ Database setup verification completed');
  } catch (error) {
    console.error('❌ Error verifying setup:', error.message);
    throw error;
  }
}

async function setupDatabase() {
  console.log('🚀 Starting Politica Database Setup');
  console.log('=' .repeat(50));
  
  try {
    await createDatabase();
    await runSchemaScript();
    await runSeedScript();
    await verifySetup();
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('You can now start the server with: npm start');
    
  } catch (error) {
    console.error('\n💥 Database setup failed:', error.message);
    process.exit(1);
  } finally {
    await setupPool.end();
    await targetPool.end();
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  require('dotenv').config();
  setupDatabase();
}

module.exports = { setupDatabase };
