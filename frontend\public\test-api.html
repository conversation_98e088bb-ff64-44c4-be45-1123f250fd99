<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Politica API Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            margin: 0;
            padding: 2rem;
            background-color: #f9fafb;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background-color: #2563eb;
        }
        
        .btn-success {
            background-color: #10b981;
        }
        
        .btn-success:hover {
            background-color: #059669;
        }
        
        .btn-danger {
            background-color: #ef4444;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
        
        .status {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .status.success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status.error {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .status.info {
            background-color: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        pre {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.875rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }
        
        input, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        input:focus, textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ Politica Platform API Test</h1>
            <p>Testing the PostgreSQL-powered backend API</p>
        </div>

        <div class="grid">
            <!-- Health Check -->
            <div class="card">
                <h3>🔍 Health Check</h3>
                <p>Test the database connection and server status.</p>
                <button class="btn" onclick="testHealth()">Check Health</button>
                <div id="health-result"></div>
            </div>

            <!-- Articles -->
            <div class="card">
                <h3>📚 Knowledge Articles</h3>
                <p>Fetch and display knowledge articles from the database.</p>
                <button class="btn" onclick="testArticles()">Get Articles</button>
                <div id="articles-result"></div>
            </div>

            <!-- Authentication -->
            <div class="card">
                <h3>🔐 Authentication</h3>
                <p>Test user registration and login functionality.</p>
                
                <div class="form-group">
                    <label for="test-email">Email:</label>
                    <input type="email" id="test-email" value="<EMAIL>" />
                </div>
                
                <div class="form-group">
                    <label for="test-username">Username:</label>
                    <input type="text" id="test-username" value="testuser" />
                </div>
                
                <div class="form-group">
                    <label for="test-password">Password:</label>
                    <input type="password" id="test-password" value="TestPassword123" />
                </div>
                
                <button class="btn" onclick="testRegister()">Register</button>
                <button class="btn btn-success" onclick="testLogin()">Login</button>
                <button class="btn btn-danger" onclick="testLogout()">Logout</button>
                
                <div id="auth-result"></div>
            </div>

            <!-- Discussions -->
            <div class="card">
                <h3>💬 Discussion Forums</h3>
                <p>Test discussion thread creation and management.</p>
                
                <div class="form-group">
                    <label for="thread-title">Thread Title:</label>
                    <input type="text" id="thread-title" value="Test Discussion Thread" />
                </div>
                
                <div class="form-group">
                    <label for="thread-description">Description:</label>
                    <textarea id="thread-description" rows="3">This is a test discussion thread created via the API test interface.</textarea>
                </div>
                
                <button class="btn" onclick="testCreateThread()">Create Thread</button>
                <button class="btn btn-success" onclick="testGetDiscussions()">Get Discussions</button>
                
                <div id="discussions-result"></div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="card">
            <h3>📊 Test Results</h3>
            <div id="overall-results">
                <div class="status info">
                    Ready to test! Click the buttons above to test different API endpoints.
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        let authToken = localStorage.getItem('politica_test_token');

        // Helper function for API calls
        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
                    ...options.headers
                },
                ...options
            };

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Display result helper
        function displayResult(elementId, result, title = '') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            
            let html = `<div class="status ${result.success ? 'success' : 'error'}">`;
            html += `<strong>${title} [${timestamp}]</strong><br>`;
            
            if (result.success) {
                html += `✅ Success (${result.status})<br>`;
                html += `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
            } else {
                html += `❌ Error: ${result.error || result.data?.error || 'Unknown error'}<br>`;
                if (result.data) {
                    html += `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
                }
            }
            
            html += '</div>';
            element.innerHTML = html;
        }

        // Test functions
        async function testHealth() {
            const result = await apiCall('/health/db');
            displayResult('health-result', result, 'Health Check');
        }

        async function testArticles() {
            const result = await apiCall('/api/articles');
            displayResult('articles-result', result, 'Articles');
        }

        async function testRegister() {
            const email = document.getElementById('test-email').value;
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            const result = await apiCall('/api/auth/register', {
                method: 'POST',
                body: JSON.stringify({ email, username, password })
            });
            
            if (result.success && result.data.token) {
                authToken = result.data.token;
                localStorage.setItem('politica_test_token', authToken);
            }
            
            displayResult('auth-result', result, 'Registration');
        }

        async function testLogin() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            const result = await apiCall('/api/auth/login', {
                method: 'POST',
                body: JSON.stringify({ email, password })
            });
            
            if (result.success && result.data.token) {
                authToken = result.data.token;
                localStorage.setItem('politica_test_token', authToken);
            }
            
            displayResult('auth-result', result, 'Login');
        }

        async function testLogout() {
            authToken = null;
            localStorage.removeItem('politica_test_token');
            displayResult('auth-result', { success: true, data: { message: 'Logged out locally' } }, 'Logout');
        }

        async function testCreateThread() {
            const title = document.getElementById('thread-title').value;
            const description = document.getElementById('thread-description').value;
            
            const result = await apiCall('/api/discussions', {
                method: 'POST',
                body: JSON.stringify({
                    title,
                    description,
                    category: 'Testing',
                    tags: ['test', 'api']
                })
            });
            
            displayResult('discussions-result', result, 'Create Thread');
        }

        async function testGetDiscussions() {
            const result = await apiCall('/api/discussions');
            displayResult('discussions-result', result, 'Get Discussions');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) {
                document.getElementById('overall-results').innerHTML = 
                    '<div class="status success">🔐 Authentication token found in localStorage</div>';
            }
        });
    </script>
</body>
</html>
