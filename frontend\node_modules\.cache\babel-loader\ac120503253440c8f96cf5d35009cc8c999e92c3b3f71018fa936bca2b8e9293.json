{"ast": null, "code": "var _jsxFileName = \"F:\\\\POLITICA\\\\VS CODE\\\\frontend\\\\src\\\\pages\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      setLoading(false);\n      return;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      setLoading(false);\n      return;\n    }\n    try {\n      const {\n        error\n      } = await supabase.auth.signUp({\n        email: formData.email,\n        password: formData.password,\n        options: {\n          data: {\n            username: formData.username\n          }\n        }\n      });\n      if (error) {\n        setError(error.message);\n      } else {\n        setSuccess('Registration successful! Please check your email to verify your account.');\n        // Optionally redirect after a delay\n        setTimeout(() => {\n          navigate('/login');\n        }, 3000);\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n        children: \"Create your account\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-center text-sm text-gray-600\",\n        children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"font-medium text-blue-600 hover:text-blue-500\",\n          children: \"sign in to your existing account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"space-y-6\",\n          onSubmit: handleSubmit,\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md\",\n            children: success\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"username\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"username\",\n                name: \"username\",\n                type: \"text\",\n                required: true,\n                value: formData.username,\n                onChange: handleChange,\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Choose a username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"email\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                autoComplete: \"email\",\n                required: true,\n                value: formData.email,\n                onChange: handleChange,\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"password\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.password,\n                onChange: handleChange,\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Create a password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-500\",\n              children: \"Must be at least 6 characters long\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"confirmPassword\",\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Confirm Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: \"password\",\n                autoComplete: \"new-password\",\n                required: true,\n                value: formData.confirmPassword,\n                onChange: handleChange,\n                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Confirm your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"terms\",\n              name: \"terms\",\n              type: \"checkbox\",\n              required: true,\n              className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"terms\",\n              className: \"ml-2 block text-sm text-gray-900\",\n              children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"text-blue-600 hover:text-blue-500\",\n                children: \"Terms of Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"text-blue-600 hover:text-blue-500\",\n                children: \"Privacy Policy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: loading ? 'Creating account...' : 'Create account'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full border-t border-gray-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative flex justify-center text-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-2 bg-white text-gray-500\",\n                children: \"Or continue with\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 grid grid-cols-2 gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2\",\n                children: \"Google\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-5 w-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2\",\n                children: \"Twitter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"2r690E4APZEHcKGpXjtqC2JnsMM=\", false, function () {\n  return [useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "supabase", "jsxDEV", "_jsxDEV", "Register", "_s", "formData", "setFormData", "username", "email", "password", "confirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "length", "auth", "signUp", "options", "data", "message", "setTimeout", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "htmlFor", "id", "type", "required", "onChange", "placeholder", "autoComplete", "disabled", "viewBox", "fill", "d", "_c", "$RefreshReg$"], "sources": ["F:/POLITICA/VS CODE/frontend/src/pages/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\n\nconst Register = () => {\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      setLoading(false);\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const { error } = await supabase.auth.signUp({\n        email: formData.email,\n        password: formData.password,\n        options: {\n          data: {\n            username: formData.username,\n          },\n        },\n      });\n\n      if (error) {\n        setError(error.message);\n      } else {\n        setSuccess('Registration successful! Please check your email to verify your account.');\n        // Optionally redirect after a delay\n        setTimeout(() => {\n          navigate('/login');\n        }, 3000);\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n          Create your account\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <Link\n            to=\"/login\"\n            className=\"font-medium text-blue-600 hover:text-blue-500\"\n          >\n            sign in to your existing account\n          </Link>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            {success && (\n              <div className=\"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md\">\n                {success}\n              </div>\n            )}\n\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n                Username\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  required\n                  value={formData.username}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Choose a username\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Create a password\"\n                />\n              </div>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                Must be at least 6 characters long\n              </p>\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Confirm your password\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center\">\n              <input\n                id=\"terms\"\n                name=\"terms\"\n                type=\"checkbox\"\n                required\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"terms\" className=\"ml-2 block text-sm text-gray-900\">\n                I agree to the{' '}\n                <button type=\"button\" className=\"text-blue-600 hover:text-blue-500\">\n                  Terms of Service\n                </button>{' '}\n                and{' '}\n                <button type=\"button\" className=\"text-blue-600 hover:text-blue-500\">\n                  Privacy Policy\n                </button>\n              </label>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Creating account...' : 'Create account'}\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-2 gap-3\">\n              <button\n                type=\"button\"\n                className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\"\n              >\n                <svg className=\"h-5 w-5\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n                <span className=\"ml-2\">Google</span>\n              </button>\n\n              <button\n                type=\"button\"\n                className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50\"\n              >\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                </svg>\n                <span className=\"ml-2\">Twitter</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMoB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1Bb,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACc,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBZ,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;;IAEd;IACA,IAAIX,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClDI,QAAQ,CAAC,wBAAwB,CAAC;MAClCF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAIP,QAAQ,CAACI,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;MAChCX,QAAQ,CAAC,6CAA6C,CAAC;MACvDF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAM;QAAEC;MAAM,CAAC,GAAG,MAAMb,QAAQ,CAAC0B,IAAI,CAACC,MAAM,CAAC;QAC3CnB,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BmB,OAAO,EAAE;UACPC,IAAI,EAAE;YACJtB,QAAQ,EAAEF,QAAQ,CAACE;UACrB;QACF;MACF,CAAC,CAAC;MAEF,IAAIM,KAAK,EAAE;QACTC,QAAQ,CAACD,KAAK,CAACiB,OAAO,CAAC;MACzB,CAAC,MAAM;QACLd,UAAU,CAAC,0EAA0E,CAAC;QACtF;QACAe,UAAU,CAAC,MAAM;UACfd,QAAQ,CAAC,QAAQ,CAAC;QACpB,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZlB,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAK+B,SAAS,EAAC,4EAA4E;IAAAC,QAAA,gBACzFhC,OAAA;MAAK+B,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/ChC,OAAA;QAAI+B,SAAS,EAAC,wDAAwD;QAAAC,QAAA,EAAC;MAEvE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpC,OAAA;QAAG+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,GAAC,IAClD,EAAC,GAAG,eACNhC,OAAA,CAACJ,IAAI;UACHyC,EAAE,EAAC,QAAQ;UACXN,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAC1D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENpC,OAAA;MAAK+B,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDhC,OAAA;QAAK+B,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DhC,OAAA;UAAM+B,SAAS,EAAC,WAAW;UAACO,QAAQ,EAAEjB,YAAa;UAAAW,QAAA,GAChDrB,KAAK,iBACJX,OAAA;YAAK+B,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAC/ErB;UAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAvB,OAAO,iBACNb,OAAA;YAAK+B,SAAS,EAAC,yEAAyE;YAAAC,QAAA,EACrFnB;UAAO;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,eAEDpC,OAAA;YAAAgC,QAAA,gBACEhC,OAAA;cAAOuC,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpC,OAAA;cAAK+B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBhC,OAAA;gBACEwC,EAAE,EAAC,UAAU;gBACbrB,IAAI,EAAC,UAAU;gBACfsB,IAAI,EAAC,MAAM;gBACXC,QAAQ;gBACRtB,KAAK,EAAEjB,QAAQ,CAACE,QAAS;gBACzBsC,QAAQ,EAAE3B,YAAa;gBACvBe,SAAS,EAAC,uKAAuK;gBACjLa,WAAW,EAAC;cAAmB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpC,OAAA;YAAAgC,QAAA,gBACEhC,OAAA;cAAOuC,OAAO,EAAC,OAAO;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpC,OAAA;cAAK+B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBhC,OAAA;gBACEwC,EAAE,EAAC,OAAO;gBACVrB,IAAI,EAAC,OAAO;gBACZsB,IAAI,EAAC,OAAO;gBACZI,YAAY,EAAC,OAAO;gBACpBH,QAAQ;gBACRtB,KAAK,EAAEjB,QAAQ,CAACG,KAAM;gBACtBqC,QAAQ,EAAE3B,YAAa;gBACvBe,SAAS,EAAC,uKAAuK;gBACjLa,WAAW,EAAC;cAAkB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpC,OAAA;YAAAgC,QAAA,gBACEhC,OAAA;cAAOuC,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpC,OAAA;cAAK+B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBhC,OAAA;gBACEwC,EAAE,EAAC,UAAU;gBACbrB,IAAI,EAAC,UAAU;gBACfsB,IAAI,EAAC,UAAU;gBACfI,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRtB,KAAK,EAAEjB,QAAQ,CAACI,QAAS;gBACzBoC,QAAQ,EAAE3B,YAAa;gBACvBe,SAAS,EAAC,uKAAuK;gBACjLa,WAAW,EAAC;cAAmB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNpC,OAAA;cAAG+B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENpC,OAAA;YAAAgC,QAAA,gBACEhC,OAAA;cAAOuC,OAAO,EAAC,iBAAiB;cAACR,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAErF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpC,OAAA;cAAK+B,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBhC,OAAA;gBACEwC,EAAE,EAAC,iBAAiB;gBACpBrB,IAAI,EAAC,iBAAiB;gBACtBsB,IAAI,EAAC,UAAU;gBACfI,YAAY,EAAC,cAAc;gBAC3BH,QAAQ;gBACRtB,KAAK,EAAEjB,QAAQ,CAACK,eAAgB;gBAChCmC,QAAQ,EAAE3B,YAAa;gBACvBe,SAAS,EAAC,uKAAuK;gBACjLa,WAAW,EAAC;cAAuB;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpC,OAAA;YAAK+B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChChC,OAAA;cACEwC,EAAE,EAAC,OAAO;cACVrB,IAAI,EAAC,OAAO;cACZsB,IAAI,EAAC,UAAU;cACfC,QAAQ;cACRX,SAAS,EAAC;YAAmE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACFpC,OAAA;cAAOuC,OAAO,EAAC,OAAO;cAACR,SAAS,EAAC,kCAAkC;cAAAC,QAAA,GAAC,gBACpD,EAAC,GAAG,eAClBhC,OAAA;gBAAQyC,IAAI,EAAC,QAAQ;gBAACV,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAAC,GAAG,EAAC,KACX,EAAC,GAAG,eACPpC,OAAA;gBAAQyC,IAAI,EAAC,QAAQ;gBAACV,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENpC,OAAA;YAAAgC,QAAA,eACEhC,OAAA;cACEyC,IAAI,EAAC,QAAQ;cACbK,QAAQ,EAAErC,OAAQ;cAClBsB,SAAS,EAAC,0QAA0Q;cAAAC,QAAA,EAEnRvB,OAAO,GAAG,qBAAqB,GAAG;YAAgB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPpC,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhC,OAAA;YAAK+B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBhC,OAAA;cAAK+B,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjDhC,OAAA;gBAAK+B,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNpC,OAAA;cAAK+B,SAAS,EAAC,sCAAsC;cAAAC,QAAA,eACnDhC,OAAA;gBAAM+B,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpC,OAAA;YAAK+B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ChC,OAAA;cACEyC,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,qJAAqJ;cAAAC,QAAA,gBAE/JhC,OAAA;gBAAK+B,SAAS,EAAC,SAAS;gBAACgB,OAAO,EAAC,WAAW;gBAAAf,QAAA,gBAC1ChC,OAAA;kBACEgD,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAyH;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5H,CAAC,eACFpC,OAAA;kBACEgD,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAuI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CAAC,eACFpC,OAAA;kBACEgD,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAA+H;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClI,CAAC,eACFpC,OAAA;kBACEgD,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAqI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNpC,OAAA;gBAAM+B,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eAETpC,OAAA;cACEyC,IAAI,EAAC,QAAQ;cACbV,SAAS,EAAC,qJAAqJ;cAAAC,QAAA,gBAE/JhC,OAAA;gBAAK+B,SAAS,EAAC,SAAS;gBAACiB,IAAI,EAAC,cAAc;gBAACD,OAAO,EAAC,WAAW;gBAAAf,QAAA,eAC9DhC,OAAA;kBAAMiD,CAAC,EAAC;gBAA4f;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACngB,CAAC,eACNpC,OAAA;gBAAM+B,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CAjQID,QAAQ;EAAA,QAUKJ,WAAW;AAAA;AAAAqD,EAAA,GAVxBjD,QAAQ;AAmQd,eAAeA,QAAQ;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}