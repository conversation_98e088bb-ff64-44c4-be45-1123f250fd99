# PostgreSQL Database Setup for Politica

This guide will help you set up the PostgreSQL database for the Politica platform.

## Prerequisites

1. **PostgreSQL installed** on your system
   - Download from: https://www.postgresql.org/download/
   - Make sure PostgreSQL service is running

2. **Database credentials** (as specified):
   - Database name: `POLITICA_AUG`
   - Username: `postgres`
   - Password: `Rayvical`
   - Host: `localhost`
   - Port: `5432`

## Setup Steps

### Step 1: Connect to PostgreSQL

Open your PostgreSQL command line tool (psql) or use a GUI tool like pgAdmin.

**Using psql:**
```bash
psql -U postgres -h localhost
```
Enter password: `Rayvical`

### Step 2: Create the Database

Run the database setup script:

```sql
-- Create the database
CREATE DATABASE "POLITICA_AUG"
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'English_United States.1252'
    LC_CTYPE = 'English_United States.1252'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;
```

### Step 3: Run the Schema Script

1. Connect to the new database:
```bash
psql -U postgres -h localhost -d POLITICA_AUG
```

2. Run the schema creation script:
```bash
\i database/postgresql_setup.sql
```

Or copy and paste the contents of `database/postgresql_setup.sql` into your PostgreSQL client.

### Step 4: Seed the Database

Run the seed data script:
```bash
\i database/postgresql_seed_data.sql
```

Or copy and paste the contents of `database/postgresql_seed_data.sql`.

## Alternative Setup Methods

### Method 1: Using pgAdmin

1. Open pgAdmin
2. Connect to your PostgreSQL server
3. Right-click on "Databases" → "Create" → "Database"
4. Name: `POLITICA_AUG`, Owner: `postgres`
5. Open Query Tool for the new database
6. Copy and paste the contents of `database/postgresql_setup.sql`
7. Execute the script
8. Copy and paste the contents of `database/postgresql_seed_data.sql`
9. Execute the script

### Method 2: Command Line (All at once)

```bash
# Create database
createdb -U postgres -h localhost POLITICA_AUG

# Run schema
psql -U postgres -h localhost -d POLITICA_AUG -f database/postgresql_setup.sql

# Run seed data
psql -U postgres -h localhost -d POLITICA_AUG -f database/postgresql_seed_data.sql
```

## Verification

After setup, verify the database is working:

1. **Check tables exist:**
```sql
\dt
```

2. **Check sample data:**
```sql
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM knowledge_articles;
SELECT COUNT(*) FROM discussion_threads;
```

3. **Test the API connection:**
Start the server and visit: http://localhost:3000/health/db

## Default Users

The seed data creates these test users:

| Username | Email | Password | Role |
|----------|-------|----------|------|
| admin | <EMAIL> | password123 | admin |
| dr_johnson | <EMAIL> | password123 | verified |
| policy_expert | <EMAIL> | password123 | verified |
| student_pol | <EMAIL> | password123 | regular |

**Note:** In production, these passwords should be changed immediately!

## Troubleshooting

### Connection Issues

1. **Check PostgreSQL is running:**
```bash
# Windows
net start postgresql-x64-14

# Linux/Mac
sudo systemctl status postgresql
```

2. **Check connection settings:**
   - Verify host, port, username, and password in `.env` file
   - Ensure PostgreSQL accepts connections on localhost:5432

3. **Check database exists:**
```sql
\l
```

### Permission Issues

If you get permission errors:

1. **Grant privileges:**
```sql
GRANT ALL PRIVILEGES ON DATABASE "POLITICA_AUG" TO postgres;
```

2. **Check pg_hba.conf** for authentication settings

### Schema Issues

If tables aren't created:

1. **Check for errors** in the schema script output
2. **Verify UUID extension** is available:
```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

## Environment Configuration

Make sure your `.env` file has the correct database settings:

```env
# PostgreSQL Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=POLITICA_AUG
DB_USER=postgres
DB_PASSWORD=Rayvical

# Server Configuration
PORT=3000
NODE_ENV=development

# Security
JWT_SECRET=politica_jwt_secret_key_2024_very_secure_random_string
SESSION_SECRET=politica_session_secret_key_2024_very_secure_random_string
```

## Next Steps

After database setup:

1. **Start the backend server:**
```bash
npm start
```

2. **Test the API:**
   - Health check: http://localhost:3000/health/db
   - Articles: http://localhost:3000/api/articles

3. **Set up the frontend** (if not already done)

4. **Create your admin account** or change default passwords

## Security Notes

- Change default passwords in production
- Use environment variables for sensitive data
- Enable SSL for database connections in production
- Regularly backup your database
- Monitor for security updates
