// Test script for Politica API with PostgreSQL
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Test data
const testUser = {
  username: 'test_user_' + Date.now(),
  email: `test${Date.now()}@example.com`,
  password: 'TestPassword123'
};

let authToken = '';

// Helper function for API calls
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 5000
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  if (authToken) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

// Test functions
async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check...');
  try {
    const response = await api.get('/health/db');
    console.log('✅ Health check passed:', response.data.message);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testGetArticles() {
  console.log('\n📚 Testing Get Articles...');
  try {
    const response = await api.get('/api/articles');
    console.log(`✅ Articles retrieved: ${response.data.count} articles found`);
    if (response.data.data.length > 0) {
      console.log('   Sample article:', response.data.data[0].title);
    }
    return true;
  } catch (error) {
    console.error('❌ Get articles failed:', error.message);
    return false;
  }
}

async function testUserRegistration() {
  console.log('\n👤 Testing User Registration...');
  try {
    const response = await api.post('/api/auth/register', testUser);
    console.log('✅ User registered successfully:', response.data.user.username);
    authToken = response.data.token;
    console.log('   Token received and stored');
    return true;
  } catch (error) {
    console.error('❌ Registration failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testUserLogin() {
  console.log('\n🔐 Testing User Login...');
  try {
    const response = await api.post('/api/auth/login', {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login successful:', response.data.user.username);
    authToken = response.data.token;
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testGetProfile() {
  console.log('\n👤 Testing Get Profile...');
  try {
    const response = await api.get('/api/auth/profile');
    console.log('✅ Profile retrieved:', response.data.user.username);
    console.log('   Role:', response.data.user.role);
    console.log('   Verified:', response.data.user.verified_status);
    return true;
  } catch (error) {
    console.error('❌ Get profile failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testUpdateProfile() {
  console.log('\n✏️ Testing Update Profile...');
  try {
    const response = await api.put('/api/auth/profile', {
      bio: 'This is a test bio for the API test user.'
    });
    console.log('✅ Profile updated successfully');
    console.log('   Bio:', response.data.user.bio);
    return true;
  } catch (error) {
    console.error('❌ Update profile failed:', error.response?.data?.error || error.message);
    return false;
  }
}

async function testInvalidLogin() {
  console.log('\n🚫 Testing Invalid Login...');
  try {
    await api.post('/api/auth/login', {
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    console.error('❌ Invalid login should have failed but didn\'t');
    return false;
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Invalid login correctly rejected');
      return true;
    } else {
      console.error('❌ Unexpected error:', error.message);
      return false;
    }
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Politica API Tests with PostgreSQL');
  console.log('=' .repeat(50));
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Get Articles', fn: testGetArticles },
    { name: 'User Registration', fn: testUserRegistration },
    { name: 'User Login', fn: testUserLogin },
    { name: 'Get Profile', fn: testGetProfile },
    { name: 'Update Profile', fn: testUpdateProfile },
    { name: 'Invalid Login', fn: testInvalidLogin }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" threw an error:`, error.message);
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! PostgreSQL integration is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the output above for details.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = { runTests };
